import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "var(--primary)",
          light: "var(--primary-light)",
          dark: "var(--primary-dark)",
          foreground: "var(--primary-foreground)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          light: "var(--secondary-light)",
          dark: "var(--secondary-dark)",
          foreground: "var(--secondary-foreground)",
        },
        tertiary: {
          DEFAULT: "var(--tertiary)",
          light: "var(--tertiary-light)",
          dark: "var(--tertiary-dark)",
          foreground: "var(--tertiary-foreground)",
        },
        // Status colors
        status: {
          success: "var(--status-success)",
          "success-light": "var(--status-success-light)",
          "success-dark": "var(--status-success-dark)",
          warning: "var(--status-warning)",
          "warning-light": "var(--status-warning-light)",
          "warning-dark": "var(--status-warning-dark)",
          error: "var(--status-error)",
          "error-light": "var(--status-error-light)",
          "error-dark": "var(--status-error-dark)",
          info: "var(--status-info)",
          "info-light": "var(--status-info-light)",
          "info-dark": "var(--status-info-dark)",
        },
        // Neutral colors
        neutral: {
          50: "var(--neutral-50)",
          100: "var(--neutral-100)",
          200: "var(--neutral-200)",
          300: "var(--neutral-300)",
          400: "var(--neutral-400)",
          500: "var(--neutral-500)",
          600: "var(--neutral-600)",
          700: "var(--neutral-700)",
          800: "var(--neutral-800)",
          900: "var(--neutral-900)",
          950: "var(--neutral-950)",
        },
        // Semantic UI colors
        text: {
          primary: "var(--text-primary)",
          secondary: "var(--text-secondary)",
          muted: "var(--text-muted)",
          inverse: "var(--text-inverse)",
        },
        bg: {
          primary: "var(--bg-primary)",
          secondary: "var(--bg-secondary)",
          card: "var(--bg-card)",
          "card-hover": "var(--bg-card-hover)",
          overlay: "var(--bg-overlay)",
        },
        border: {
          primary: "var(--border-primary)",
          secondary: "var(--border-secondary)",
          subtle: "var(--border-subtle)",
        },
        // Button colors
        btn: {
          primary: "var(--btn-primary)",
          "primary-hover": "var(--btn-primary-hover)",
          "primary-text": "var(--btn-primary-text)",
          secondary: "var(--btn-secondary)",
          "secondary-hover": "var(--btn-secondary-hover)",
          "secondary-text": "var(--btn-secondary-text)",
          tertiary: "var(--btn-tertiary)",
          "tertiary-hover": "var(--btn-tertiary-hover)",
          "tertiary-text": "var(--btn-tertiary-text)",
          success: "var(--btn-success)",
          "success-hover": "var(--btn-success-hover)",
          "success-text": "var(--btn-success-text)",
          warning: "var(--btn-warning)",
          "warning-hover": "var(--btn-warning-hover)",
          "warning-text": "var(--btn-warning-text)",
          error: "var(--btn-error)",
          "error-hover": "var(--btn-error-hover)",
          "error-text": "var(--btn-error-text)",
          ghost: "var(--btn-ghost)",
          "ghost-hover": "var(--btn-ghost-hover)",
          "ghost-text": "var(--btn-ghost-text)",
        },
        // Role colors
        role: {
          admin: "var(--role-admin)",
          "admin-light": "var(--role-admin-light)",
          "admin-dark": "var(--role-admin-dark)",
          editor: "var(--role-editor)",
          "editor-light": "var(--role-editor-light)",
          "editor-dark": "var(--role-editor-dark)",
          viewer: "var(--role-viewer)",
          "viewer-light": "var(--role-viewer-light)",
          "viewer-dark": "var(--role-viewer-dark)",
          content: "var(--role-content)",
          "content-light": "var(--role-content-light)",
          "content-dark": "var(--role-content-dark)",
        },
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: 0, transform: "translateY(-5px)" },
          "100%": { opacity: 1, transform: "translateY(0)" },
        },
      },
      animation: {
        fadeIn: "fadeIn 0.2s ease-out forwards",
      },
    },
  },
  plugins: [],
};

export default config;
