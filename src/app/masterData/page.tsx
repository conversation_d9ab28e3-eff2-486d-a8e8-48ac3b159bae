"use client";
import { TabNavigation } from "@/components/constant/tab-navigation";
import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import TimeTable from "./time-table";

const tabs = [
  { id: "timetables", label: "Timetables" },
  {
    id: "sponsors",
    label: "Sponsors",
  },
  {
    id: "prizes",
    label: "Prizes",
  },
  {
    id: "tracks",
    label: "Tracks",
  },
];

export default function MasterDataPage() {
  const [activeTab, setActiveTab] = useState<string>("timetables");

  const renderTabContent = () => {
    switch (activeTab) {
      case "sponsors":
        return (
          <div>
            <h1>sponsors</h1>
          </div>
        );
      case "timetables":
        return <TimeTable />;
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      MasterDataPage
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
      {renderTabContent()}
    </MainLayout>
  );
}
