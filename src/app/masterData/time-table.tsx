"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Edit, Trash2 } from "lucide-react";

export default function TimeTable() {
  const [date, setDate] = useState<Date>();
  const [examName, setExamName] = useState("");
  const [timeTables, setTimeTables] = useState([
    { id: 1, name: "Weekly", date: "25-07-25" },
    { id: 2, name: "Monthly", date: "30-08-25" },
    { id: 3, name: "Weekly", date: "15-11-25" },
    { id: 4, name: "Weekly", date: "22-12-25" },
  ]);

  const handleAdd = () => {
    if (!examName || !date) return;
    const newEntry = {
      id: Date.now(),
      name: examName,
      date: format(date, "dd-MM-yy"),
    };
    setTimeTables([...timeTables, newEntry]);
    setExamName("");
    setDate(undefined);
  };

  return (
    <div className="space-y-6 mt-4">
      {/* Input Section */}
      <div className="flex justify-between items-end gap-6">
        <div className="flex flex-col flex-1">
          <label className="text-sm font-medium text-text-primary mb-2">
            Enter Exam Name
          </label>
          <Input
            value={examName}
            onChange={(e) => setExamName(e.target.value)}
            placeholder="Enter Exam Name"
            className="bg-bg-secondary border border-border-primary text-text-primary"
          />
        </div>

        <div className="flex flex-col flex-1">
          <label className="text-sm font-medium text-text-primary mb-2">
            Exam Date
          </label>
          <Popover>
            <PopoverTrigger asChild>
              <button className="flex items-center justify-between px-3 py-2 bg-bg-card border border-border-primary rounded-md text-sm text-text-primary w-full">
                {date ? format(date, "dd/MM/yy") : "dd/mm/yy"}
                <CalendarIcon className="h-4 w-4 opacity-70" />
              </button>
            </PopoverTrigger>
            <PopoverContent className="p-0 bg-bg-card border border-border-primary rounded-xl">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className="flex justify-end gap-3">
        <Button
          variant="cancel"
          onClick={() => {
            setExamName("");
            setDate(undefined);
          }}
        >
          Cancel
        </Button>
        <Button variant="success" onClick={handleAdd}>
          Add
        </Button>
      </div>

      {/* Time Table List */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-text-primary">Time Table</h2>
        <p className="text-sm text-text-secondary">
          Manage Existing all Timetable
        </p>

        {timeTables.map((item) => (
          <Card
            key={item.id}
            className="bg-bg-secondary border border-border-primary rounded-xl shadow-sm"
          >
            <CardContent className="flex justify-between items-center p-4">
              <div className="space-y-1 text-text-primary text-sm">
                <p>
                  <span className="font-medium">Exam Name :- </span>
                  {item.name}
                </p>
                <p>
                  <span className="font-medium">Exam Date : </span>
                  {item.date}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="secondary" size="sm">
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-1" /> Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
