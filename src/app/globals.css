@import "tailwindcss";
@import "tw-animate-css";
@import "../styles/colors.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
  --card: var(--bg-card);
  --card-foreground: var(--text-primary);
  --popover: var(--bg-secondary);
  --popover-foreground: var(--text-primary);
  --muted: var(--neutral-100);
  --muted-foreground: var(--text-muted);
  --accent: var(--bg-card);
  --accent-foreground: var(--text-primary);
  --destructive: var(--status-error);
  --border: var(--border-primary);
  --input: var(--border-primary);
  --ring: var(--primary);
  --chart-1: var(--status-success);
  --chart-2: var(--status-info);
  --chart-3: var(--role-admin);
  --chart-4: var(--status-warning);
  --chart-5: var(--tertiary);
  --sidebar: var(--bg-primary);
  --sidebar-foreground: var(--text-primary);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--bg-card);
  --sidebar-accent-foreground: var(--text-primary);
  --sidebar-border: var(--border-primary);
  --sidebar-ring: var(--primary);
}

.dark {
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
  --card: var(--bg-card);
  --card-foreground: var(--text-primary);
  --popover: var(--bg-secondary);
  --popover-foreground: var(--text-primary);
  --muted: var(--neutral-800);
  --muted-foreground: var(--text-muted);
  --accent: var(--bg-card);
  --accent-foreground: var(--text-primary);
  --destructive: var(--status-error);
  --border: var(--border-primary);
  --input: var(--border-primary);
  --ring: var(--primary);
  --chart-1: var(--status-success);
  --chart-2: var(--status-info);
  --chart-3: var(--role-admin);
  --chart-4: var(--status-warning);
  --chart-5: var(--tertiary);
  --sidebar: var(--bg-primary);
  --sidebar-foreground: var(--text-primary);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--bg-card);
  --sidebar-accent-foreground: var(--text-primary);
  --sidebar-border: var(--border-primary);
  --sidebar-ring: var(--primary);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Ensure full width for main content */
@layer utilities {
  .main-content {
    width: 100% !important;
    max-width: none !important;
  }

  .full-width-container {
    width: 100% !important;
    max-width: none !important;
  }
}
