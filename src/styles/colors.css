/* Custom Color Variables */
/* You can easily edit your brand colors here */

:root {
  /* Primary Colors - Light Blue Theme */
  --primary: #d7e1e4;
  --primary-light: #e5edef;
  --primary-dark: #c1cdd2;
  --primary-foreground: #1a1a1a;

  /* Secondary Colors - Light Blue Theme */
  --secondary: #d1e4eb;
  --secondary-light: #e0edf2;
  --secondary-dark: #b8d4dd;
  --secondary-foreground: #1a1a1a;

  /* Tertiary Colors - Green Theme */
  --tertiary: #016e01;
  --tertiary-light: #4a8f4a;
  --tertiary-dark: #014501;
  --tertiary-foreground: #ffffff;

  /* Status Colors */
  --status-success: #22c55e;
  --status-success-light: #dcfce7;
  --status-success-dark: #15803d;
  --status-warning: #eab308;
  --status-warning-light: #fef3c7;
  --status-warning-dark: #a16207;
  --status-error: #ef4444;
  --status-error-light: #fecaca;
  --status-error-dark: #dc2626;
  --status-info: #3b82f6;
  --status-info-light: #dbeafe;
  --status-info-dark: #1d4ed8;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;

  /* Semantic UI Colors */
  --text-primary: #ffffff;
  --text-secondary: #9ca3af;
  --text-muted: #6b7280;
  --text-inverse: #1a1a1a;
  --bg-primary: #080f17;
  --bg-secondary: #1a1a1a;
  --bg-card: rgba(255, 255, 255, 0.04);
  --bg-card-hover: rgba(255, 255, 255, 0.08);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.2);
  --border-subtle: rgba(255, 255, 255, 0.1);

  /* Button Colors */
  --btn-primary: #d7e1e4;
  --btn-primary-hover: #c1cdd2;
  --btn-primary-text: #1a1a1a;
  --btn-secondary: #d1e4eb;
  --btn-secondary-hover: #b8d4dd;
  --btn-secondary-text: #1a1a1a;
  --btn-tertiary: #016e01;
  --btn-tertiary-hover: #014501;
  --btn-tertiary-text: #ffffff;
  --btn-success: #22c55e;
  --btn-success-hover: #16a34a;
  --btn-success-text: #ffffff;
  --btn-warning: #eab308;
  --btn-warning-hover: #ca8a04;
  --btn-warning-text: #1a1a1a;
  --btn-error: #ef4444;
  --btn-error-hover: #dc2626;
  --btn-error-text: #ffffff;
  --btn-ghost: transparent;
  --btn-ghost-hover: rgba(255, 255, 255, 0.1);
  --btn-ghost-text: #d7e1e4;

  /* Role Colors */
  --role-admin: #a855f7;
  --role-admin-light: #f3e8ff;
  --role-admin-dark: #7c3aed;
  --role-editor: #3b82f6;
  --role-editor-light: #dbeafe;
  --role-editor-dark: #1d4ed8;
  --role-viewer: #6b7280;
  --role-viewer-light: #f3f4f6;
  --role-viewer-dark: #374151;
  --role-content: #8b5cf6;
  --role-content-light: #ede9fe;
  --role-content-dark: #6d28d9;
}

.dark {
  /* Primary Colors for Dark Mode */
  --primary: #d7e1e4;
  --primary-light: #e5edef;
  --primary-dark: #c1cdd2;
  --primary-foreground: #1a1a1a;

  /* Secondary Colors for Dark Mode */
  --secondary: #d1e4eb;
  --secondary-light: #e0edf2;
  --secondary-dark: #b8d4dd;
  --secondary-foreground: #1a1a1a;

  /* Tertiary Colors for Dark Mode */
  --tertiary: #016e01;
  --tertiary-light: #4a8f4a;
  --tertiary-dark: #014501;
  --tertiary-foreground: #ffffff;

  /* Status Colors for Dark Mode - Same as light mode */
  --status-success: #22c55e;
  --status-success-light: #dcfce7;
  --status-success-dark: #15803d;
  --status-warning: #eab308;
  --status-warning-light: #fef3c7;
  --status-warning-dark: #a16207;
  --status-error: #ef4444;
  --status-error-light: #fecaca;
  --status-error-dark: #dc2626;
  --status-info: #3b82f6;
  --status-info-light: #dbeafe;
  --status-info-dark: #1d4ed8;

  /* Semantic UI Colors for Dark Mode */
  --text-primary: #ffffff;
  --text-secondary: #9ca3af;
  --text-muted: #6b7280;
  --text-inverse: #1a1a1a;
  --bg-primary: #080f17;
  --bg-secondary: #1a1a1a;
  --bg-card: rgba(255, 255, 255, 0.04);
  --bg-card-hover: rgba(255, 255, 255, 0.08);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.2);
  --border-subtle: rgba(255, 255, 255, 0.1);

  /* Button Colors for Dark Mode */
  --btn-primary: #d7e1e4;
  --btn-primary-hover: #e5edef;
  --btn-primary-text: #1a1a1a;
  --btn-secondary: #d1e4eb;
  --btn-secondary-hover: #e0edf2;
  --btn-secondary-text: #1a1a1a;
  --btn-tertiary: #016e01;
  --btn-tertiary-hover: #4a8f4a;
  --btn-tertiary-text: #ffffff;
  --btn-success: #22c55e;
  --btn-success-hover: #16a34a;
  --btn-success-text: #ffffff;
  --btn-warning: #eab308;
  --btn-warning-hover: #ca8a04;
  --btn-warning-text: #1a1a1a;
  --btn-error: #ef4444;
  --btn-error-hover: #dc2626;
  --btn-error-text: #ffffff;
  --btn-ghost: transparent;
  --btn-ghost-hover: rgba(255, 255, 255, 0.1);
  --btn-ghost-text: #d7e1e4;

  /* Role Colors for Dark Mode - Same as light mode */
  --role-admin: #a855f7;
  --role-admin-light: #f3e8ff;
  --role-admin-dark: #7c3aed;
  --role-editor: #3b82f6;
  --role-editor-light: #dbeafe;
  --role-editor-dark: #1d4ed8;
  --role-viewer: #6b7280;
  --role-viewer-light: #f3f4f6;
  --role-viewer-dark: #374151;
  --role-content: #8b5cf6;
  --role-content-light: #ede9fe;
  --role-content-dark: #6d28d9;
}
