"use client";

import React, { useState } from "react";
import { X } from "lucide-react";

interface TeamMemberFormProps {
  mode: "create" | "edit";
  initialData?: {
    fullName?: string;
    email?: string;
    password?: string;
    role?: string;
  };
  onClose: () => void;
  onSubmit: (data: {
    fullName: string;
    email: string;
    password: string;
    role: string;
  }) => void;
}

export default function TeamMemberForm({
  mode,
  initialData,
  onClose,
  onSubmit,
}: TeamMemberFormProps) {
  const [formData, setFormData] = useState({
    fullName: initialData?.fullName || "",
    email: initialData?.email || "",
    password: initialData?.password || "",
    role: initialData?.role || "Member",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-bg-overlay backdrop-blur-sm"></div>
      <div className="relative bg-bg-card backdrop-blur-sm border border-border-secondary shadow-xl rounded-2xl p-8 w-full max-w-2xl z-10">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-text-primary capitalize">
            {mode === "create" ? "Add New Team Member" : "Edit Team Member"}
          </h2>
          <button onClick={onClose}>
            <X className="w-6 h-6 text-text-primary hover:text-text-secondary" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-4 flex-1 overflow-y-auto p-2">
          <div>
            <label className="block text-sm text-text-secondary mb-2">
              Full Name
            </label>
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              placeholder="Enter full name"
              className="w-full p-2 rounded-lg bg-bg-secondary text-text-primary border border-border-primary focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          <div>
            <label className="block text-sm text-text-secondary mb-2">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              className="w-full p-2 rounded-lg bg-bg-secondary text-text-primary border border-border-primary focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          <div>
            <label className="block text-sm text-text-secondary mb-2">
              Password
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter password"
              className="w-full p-2 rounded-lg bg-bg-secondary text-text-primary border border-border-primary focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          <div>
            <label className="block text-sm text-text-secondary mb-2">
              Role
            </label>
            <select
              name="role"
              value={formData.role}
              onChange={handleChange}
              className="w-full p-2 rounded-lg bg-bg-secondary text-text-primary border border-border-primary focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="Admin">Admin</option>
              <option value="Manager">Manager</option>
              <option value="Member">Member</option>
              <option value="Viewer">Viewer</option>
            </select>
          </div>
        </div>

        {/* Actions - Stick to bottom */}
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-neutral-300 text-neutral-800 rounded-lg hover:bg-neutral-400"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-btn-success text-btn-success-text rounded-lg hover:bg-btn-success-hover"
          >
            {mode === "create" ? "Create" : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
}
