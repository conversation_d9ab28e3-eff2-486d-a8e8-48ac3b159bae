import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-btn-primary text-btn-primary-text shadow-xs hover:bg-btn-primary-hover transition-all duration-200",
        primary:
          "bg-btn-primary text-btn-primary-text shadow-xs hover:bg-btn-primary-hover transition-all duration-200",
        secondary:
          "bg-btn-secondary text-btn-secondary-text shadow-xs hover:bg-btn-secondary-hover transition-all duration-200",
        tertiary:
          "bg-btn-tertiary text-btn-tertiary-text shadow-xs hover:bg-btn-tertiary-hover transition-all duration-200",
        success:
          "bg-btn-success text-btn-success-text shadow-xs hover:bg-btn-success-hover transition-all duration-200",
        warning:
          "bg-btn-warning text-btn-warning-text shadow-xs hover:bg-btn-warning-hover transition-all duration-200",
        destructive:
          "bg-btn-error text-btn-error-text shadow-xs hover:bg-btn-error-hover transition-all duration-200",
        outline:
          "border border-btn-primary text-btn-primary bg-transparent shadow-xs hover:bg-btn-primary hover:text-btn-primary-text transition-all duration-200",
        "outline-secondary":
          "border border-btn-secondary text-btn-secondary bg-transparent shadow-xs hover:bg-btn-secondary hover:text-btn-secondary-text transition-all duration-200",
        ghost:
          "bg-btn-ghost text-btn-ghost-text hover:bg-btn-ghost-hover transition-all duration-200",
        link: "text-btn-primary underline-offset-4 hover:underline bg-transparent shadow-none",
        cancel:
          "bg-neutral-300 text-neutral-800 shadow-xs hover:bg-neutral-400 transition-all duration-200",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
